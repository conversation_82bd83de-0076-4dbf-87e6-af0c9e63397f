.container {
  background-color: #f4f4f4;
  padding: 20rpx;
  font-family: Arial, sans-serif;
}

.order-header {
  text-align: center;
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.order-info {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(47, 131, 255, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 100%;
  line-height: 50rpx;
  margin-top: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

.paid-money {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.magin-bottom {
  margin-bottom: 20rpx;
}

.order-details {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  min-height: 60rpx;
  padding: 8rpx 0;
  gap: 20rpx;
  width: 100%;
}

.label {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  margin-right: 20rpx;
  line-height: 1.4;
  margin-right: 100rpx;
}

.label-row {
  flex: 1;
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.content {
  display: flex;
  align-items: baseline;
  max-width: 50%;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #007aff;
  padding: 10rpx 16rpx;
  border: none;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.08), rgba(0, 122, 255, 0.12));
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  min-width: 80rpx;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.edit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.edit-btn:active::before {
  left: 100%;
}

.edit-btn:active {
  transform: scale(0.96);
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.15), rgba(0, 122, 255, 0.2));
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.25);
}

.edit-text {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1;
}
/* 底部操作按钮容器 */
.bottom-action-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.action-buttons-row {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  padding-bottom: 20rpx;
}

/* 主要操作按钮样式 */
.primary-action-btn {
  flex: 1;
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 20rpx 20rpx;
  border-radius: 50rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  margin-right: 16rpx;
}

.primary-action-btn:last-child {
  margin-right: 0;
}

.primary-action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 联系客户按钮 - 粉色渐变 */
.primary-action-btn.contact-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff4081);
  color: white;
}

/* 导航按钮 - 绿色渐变 */
.primary-action-btn.navigation-btn {
  background: linear-gradient(135deg, #4caf50, #388e3c);
  color: white;
}

/* 查看评价按钮 - 橙色渐变 */
.primary-action-btn.review-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
}

/* 单个按钮样式 */
.single-action-btn {
  width: 100%;
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.single-action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 联系客户按钮 - 粉色渐变 */
.single-action-btn.contact-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff4081);
  color: white;
}

/* 查看评价按钮 - 橙色渐变 */
.single-action-btn.review-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
}

/* 更多操作按钮容器 */
.more-actions-wrapper {
  position: relative;
  flex-shrink: 0;
}

.more-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx 20rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  background: linear-gradient(135deg, #9e9e9e, #757575);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  min-width: 120rpx;
}

.more-action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.dropdown-icon {
  font-size: 20rpx;
  transition: transform 0.3s ease;
}

.dropdown-icon.rotate {
  transform: rotate(180deg);
}

/* 下拉菜单样式 */
.more-actions-dropdown {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 16rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  min-width: 280rpx;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10rpx);
  transition: all 0.3s ease;
  z-index: 1000;
}

.more-actions-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:active {
  background-color: #f8f9fa;
}

.dropdown-icon-img {
  width: 32rpx;
  height: 32rpx;
}

.dropdown-icon-text {
  font-size: 32rpx;
  width: 32rpx;
  text-align: center;
}

/* 下拉菜单遮罩层 */
.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.btn-icon {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  width: 36rpx;
  height: 36rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.more-btn {
  margin-right: 20rpx;
  font-size: 32rpx;
  position: relative;
  color: rgba(102, 102, 102, 1);
}
.more-actions-dropdown {
  position: absolute;
  bottom: 110rpx;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

.hidden {
  display: none;
}

/* 为底部按钮留出空间 */
.container {
  padding-bottom: 180rpx;
}

/* 原价样式 */
.original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 28rpx;
}

/* 追加服务样式 */
.additional-services,
.all-additional-services {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.service-item {
  border: 1rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.service-item:last-child {
  margin-bottom: 0;
}

.service-item:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.service-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.service-status.pending {
  background: #ff9500;
}

.service-status.confirmed {
  background: #4CAF50;
}

.service-status.rejected {
  background: #f44336;
}

.service-status.pending_payment {
  background: #FF5722;
}

.service-status.paid {
  background: #34c759;
}

.service-status.completed {
  background: #2196F3;
}

.service-status.cancelled {
  background: #9E9E9E;
}

.service-status.refunding {
  background: #FF9800;
}

.service-status.refunded {
  background: #607D8B;
}

/* 客户信息样式 */
.service-customer-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.customer-label {
  font-size: 26rpx;
  color: #666;
  /* width: 5em; */
  flex-shrink: 0;
  /* text-align: right; */
}

.customer-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 8rpx;
}

.customer-phone {
  font-size: 24rpx;
  color: #999;
}

/* 服务详情样式 */
.service-details {
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  /* width: 5em; */
  flex-shrink: 0;
  /* text-align: right; */
}

.detail-value {
  font-size: 26rpx;
  color: #333;
}

.detail-time {
  color: #999;
  font-size: 24rpx;
}

/* 价格信息样式 */
.service-price-info {
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #fff8f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff9500;
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rpx;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.current-price {
  font-size: 28rpx;
  color: #ff4444;
  font-weight: bold;
}



.service-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 20rpx;
}

/* 追加服务操作按钮 - 与系统按钮风格保持一致 */
.service-action-btn {
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  text-align: center;
  min-width: 120rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

/* 确认按钮 - 使用系统主色调 */
.service-action-btn.confirm-btn {
  background: rgba(47, 131, 255, 1);
  color: white;
}

.service-action-btn.confirm-btn:active {
  background: rgba(47, 131, 255, 0.8);
  transform: scale(0.95);
}

/* 拒绝按钮 - 使用灰色调 */
.service-action-btn.reject-btn {
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
}

.service-action-btn.reject-btn:active {
  background: rgba(220, 220, 220, 1);
  transform: scale(0.95);
}



/* 拒绝原因模态框样式 */
.reject-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2rpx);
}

.modal-content {
  position: relative;
  width: 640rpx;
  max-width: 90vw;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  padding: 40rpx 32rpx 24rpx;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 1rpx;
}

.modal-body {
  padding: 32rpx;
}

.reject-textarea {
  width: 100%;
  min-height: 240rpx;
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  box-sizing: border-box;
  background: #fafbfc;
  transition: all 0.2s ease;
}

.reject-textarea:focus {
  border-color: rgba(47, 131, 255, 0.6);
  background: #fff;
  box-shadow: 0 0 0 4rpx rgba(47, 131, 255, 0.1);
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  background: #fafbfc;
}

.modal-btn {
  flex: 1;
  font-size: 28rpx;
  border: none;
  border-radius: 40rpx;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  font-weight: 500;
}

.modal-btn.cancel-btn {
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
}

.modal-btn.cancel-btn:active {
  background: rgba(220, 220, 220, 1);
  transform: scale(0.95);
}

.modal-btn.confirm-btn {
  background: rgba(255, 68, 68, 1);
  color: white;
  font-weight: 600;
}

.modal-btn.confirm-btn:active {
  background: rgba(255, 68, 68, 0.8);
  transform: scale(0.95);
}

/* 拒绝原因样式 */
.reject-reason {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #fff5f5;
  border-radius: 8rpx;
  border-left: 4rpx solid #f44336;
}

.reason-label {
  font-size: 26rpx;
  color: #666;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.reason-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 特殊情况说明样式 */
.special-note-section,
.special-note-entry {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.special-note-content {
  border: 1rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 24rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.note-info {
  margin-bottom: 20rpx;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.note-employee {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.note-time {
  font-size: 24rpx;
  color: #999;
}

.note-text {
  margin-bottom: 16rpx;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
}

.note-photos {
  margin-top: 16rpx;
}

.photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.note-photo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  object-fit: cover;
  border: 1rpx solid #e8e8e8;
}

.note-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.note-action-btn {
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  text-align: center;
  min-width: 120rpx;
  background: rgba(255, 152, 0, 1);
  color: white;
  transition: all 0.2s ease;
}

.note-action-btn:active {
  background: rgba(255, 152, 0, 0.8);
  transform: scale(0.95);
}

.note-action-btn.view-btn {
  background: rgba(47, 131, 255, 1);
}

.note-action-btn.view-btn:active {
  background: rgba(47, 131, 255, 0.8);
}

/* 特殊情况说明入口样式 */
.entry-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.2s ease;
}

.entry-content:active {
  background: #f0f0f0;
  border-color: #ccc;
}

.entry-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.entry-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}

.entry-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 用户备注样式 */
.user-remark {
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  color: #333;
  font-size: 28rpx;
}

/* 评价区域样式 */
.review-section {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.review-loading {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.review-content {
  padding: 20rpx 0;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.rating-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.rating-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.rating-stars {
  font-size: 32rpx;
  color: #ffb400;
  margin-right: 10rpx;
}

.rating-score {
  font-size: 24rpx;
  color: #666;
}

.review-time {
  font-size: 24rpx;
  color: #999;
}

.review-text {
  margin-bottom: 20rpx;
}

.review-content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
}

.review-images {
  margin-top: 20rpx;
}

.images-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.review-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.no-review {
  text-align: center;
  padding: 60rpx 0;
}

.no-review-text {
  color: #999;
  font-size: 28rpx;
}

/* ==================== 服务时长统计样式 ==================== */

.service-duration-section {
  background-color: #fff;
  margin: 20rpx 0;
  border-radius: 10rpx;
  overflow: hidden;
}

.service-duration-section .section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  cursor: pointer;
}

.debug-refresh-btn {
  margin-left: 20rpx;
  font-size: 24rpx !important;
  padding: 8rpx 16rpx !important;
  height: auto !important;
  line-height: 1.2 !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  border: 1rpx solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
}

.toggle-icon {
  font-size: 24rpx;
  transition: transform 0.3s ease;
}

.main-service-duration,
.additional-service-duration,
.completed-services-summary {
  padding: 0 20rpx 20rpx 20rpx;
}

.service-section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  padding: 20rpx 0 15rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 15rpx;
  position: relative;
}

.service-section-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2rpx;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

.service-duration-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 10rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.service-duration-item.main-service {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-color: #1890ff;
}

.service-duration-item.additional-service {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-color: #ff9800;
  border-width: 2rpx;
}

.duration-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.service-name-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.service-type {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #ff9800, #ff5722);
  color: #fff;
  margin-left: 12rpx;
  font-weight: 500;
}

/* 服务状态样式 */
.service-status {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  align-self: flex-start;
  font-weight: 500;
}

.service-status.not_started {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: #fff;
}

.service-status.running {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #fff;
  animation: pulse 2s infinite;
}

.service-status.completed {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: #fff;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.start-time,
.time-range,
.current-duration,
.duration {
  font-size: 24rpx;
  color: #666;
}

.current-duration {
  color: #1890ff;
  font-weight: bold;
}

.duration {
  color: #52c41a;
  font-weight: bold;
}

.employee-info {
  margin-top: 8rpx;
}

.employee-name {
  font-size: 22rpx;
  color: #999;
}

.remark {
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 6rpx;
}

.remark-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.duration-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.duration-actions .action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80rpx;
}

.end-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.end-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.3);
}

.start-btn {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 184, 148, 0.3);
}

.start-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 184, 148, 0.3);
}

/* 服务时长汇总样式 */
.summary-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 10rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 26rpx;
  color: #666;
}

.summary-value {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}
