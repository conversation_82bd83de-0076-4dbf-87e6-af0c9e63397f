import { formatNormalDate } from '../../utils/util';
import orderApi from '../../../api/modules/order';
import reviewApi from '../../../api/modules/review';
import specialNoteApi from '../../../api/modules/specialNote';
import serviceDurationApi from '../../../api/modules/serviceDuration';
import Session from '../../../common/Session';
import AddressUtils from '../../../utils/AddressUtils.js';

Page({
  data: {
    orderDetail: {}, // 订单
    showMoreActions: false,
    // 时间选择器相关
    showTimePicker: false, // 是否显示时间选择器
    selectedTime: '', // 选择的时间
    userInfo: null, // 用户信息
    // 追加服务相关
    pendingAdditionalServices: [], // 待确认的追加服务列表
    allAdditionalServices: [], // 所有追加服务列表
    confirmedAdditionalServiceOrders: [], // 已确认的追加服务订单（不包括主订单增项）
    showRejectModal: false, // 是否显示拒绝原因输入框
    rejectReason: '', // 拒绝原因
    currentAdditionalService: null, // 当前操作的追加服务
    // 特殊情况说明相关
    specialNoteData: null, // 特殊情况说明数据
    showSpecialNote: false, // 是否显示特殊情况说明组件
    specialNoteReadonly: false, // 特殊情况说明是否为只读模式

    // 地址编辑器相关
    showAddressEditor: false, // 是否显示地址编辑器

    // 请求状态控制（防止重复请求）
    loadingStates: {
      orderDetail: false,
      serviceDurationRecords: false,
      additionalServices: false,
      orderServiceStatus: false
    },

    // 更多操作菜单相关
    showMoreActions: false, // 是否显示更多操作菜单

    // 评价相关
    reviewData: null, // 评价数据
    hasReview: false, // 是否有评价
    reviewLoading: false, // 评价加载状态

    // 服务时长统计相关
    serviceDurationRecords: [], // 服务时长记录列表
    mainServiceRecords: [], // 主服务时长记录
    additionalServiceRecords: [], // 增项服务时长记录
    serviceDurationStatistics: {}, // 服务时长统计信息
    showServiceDuration: true, // 是否显示服务时长统计（默认展开）
    serviceDurationTimer: null, // 服务时长计时器
  },

  onLoad(options) {
    // 获取用户信息
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    this.loadOrderDetail(options.orderId);
  },

  onShow() {
    // 页面显示时刷新数据（从支付页面返回时会触发）
    if (this.data.orderDetail && this.data.orderDetail.id) {
      console.log('📱 页面重新显示，刷新订单数据（已优化，避免重复请求）');
      // 只调用一次 loadOrderDetail，它会根据状态自动加载相应的数据
      this.loadOrderDetail(this.data.orderDetail.id);
    }

    // 启动实时计时器
    this.startRealtimeTimer();
  },

  onHide() {
    // 页面隐藏时停止计时器
    this.stopRealtimeTimer();
  },

  // 设置订单详情数据
  setOrderDetail(info) {
    // 使用从订单列表传递过来的追加服务信息
    const pendingAdditionalServices = info.pendingAdditionalServices || [];

    const orderDetailData = {
      ...info,
      additionalServices:
        info.orderDetails?.flatMap(detail => detail.additionalServices?.map(v => v.name) || []) || [],
      petName: info.orderDetails?.map(item => item.petName)[0] || '',
      serviceTime: info.serviceTime ? formatNormalDate(info.serviceTime) : null,
      // 下单时间
      orderTime: info.orderTime ? formatNormalDate(info.orderTime) : null,
      createdAt: info.createdAt ? formatNormalDate(info.createdAt) : null,
      originalPrice: info.originalPrice, // 原价
      totalFee: info.totalFee, // 实付金额
      // 确保userRemark字段被保留
      userRemark: info.userRemark,
    };

    this.setData({
      orderDetail: orderDetailData,
      pendingAdditionalServices: pendingAdditionalServices.map(item => ({
        ...item,
        createdAt: item.createdAt ? formatNormalDate(item.createdAt) : null,
      })),
    });

    // 如果订单有追加服务，则加载所有追加服务
    if (info.hasAdditionalServices && info.orderDetails && info.orderDetails.length > 0) {
      console.log('🔍 检测到订单有增项服务，开始加载...');
      this.loadAllAdditionalServices(info.orderDetails[0].id);
    } else {
      console.log('🔍 增项服务加载条件检查:', {
        hasAdditionalServices: info.hasAdditionalServices,
        hasOrderDetails: info.orderDetails && info.orderDetails.length > 0,
        orderDetailsLength: info.orderDetails?.length || 0
      });

      // 即使没有hasAdditionalServices标记，也尝试加载一次增项服务
      if (info.orderDetails && info.orderDetails.length > 0) {
        console.log('🔍 尝试强制加载增项服务...');
        this.loadAllAdditionalServices(info.orderDetails[0].id);
      }
    }

    // 加载特殊情况说明
    this.loadSpecialNote(info.id);

    // 如果是已完成或已评价状态，加载评价信息
    if (info.status === '已完成' || info.status === '已评价') {
      this.loadReviewData(info.id);
    }

    // 根据订单状态选择合适的服务数据加载方式
    if (info.status === '服务中') {
      // 服务中状态优先使用新的订单服务状态接口，获取更完整的信息
      this.loadOrderServiceStatus(info.id);
      // 同时加载详细的服务时长记录作为补充
      this.loadServiceDurationRecords(info.id);
      // 启动计时器
      this.startServiceTimer();
    } else {
      // 其他状态使用原有的服务时长记录接口
      this.loadServiceDurationRecords(info.id);
    }
  },

  // 加载所有追加服务
  async loadAllAdditionalServices(orderDetailId) {
    // 防止重复请求
    if (this.data.loadingStates.additionalServices) {
      console.log('🔍 增项服务正在加载中，跳过重复请求');
      return;
    }

    try {
      // 设置加载状态
      this.setData({
        'loadingStates.additionalServices': true
      });

      console.log('🔍 开始加载增项服务，orderDetailId:', orderDetailId);
      const res = await orderApi.getAdditionalServices(orderDetailId);
      console.log('🔍 增项服务API返回结果:', res);

      if (res && typeof res === 'object') {
        const { originalAdditionalServices = [], additionalServiceOrders = [], summary = {} } = res;

        console.log('🔍 新接口数据结构:', {
          主订单增项服务: originalAdditionalServices.length,
          追加服务订单: additionalServiceOrders.length,
          统计信息: summary
        });

        // 处理主订单的增项服务（已包含在主订单中，无需额外支付）
        const formattedOriginalServices = originalAdditionalServices.map(item => ({
          ...item,
          type: 'original', // 标记为主订单增项服务
          status: 'confirmed', // 主订单增项服务默认已确认
          statusText: '已确认',
          serviceName: item.name,
          servicePrice: item.price,
          needDurationTracking: true, // 需要时长统计
          additionalServiceId: item.id, // 用于时长统计API
          orderDetailId: orderDetailId, // 主订单详情ID，用于API调用
          createdAt: null,
          confirmTime: null,
        }));

        console.log('🔍 主订单增项服务处理结果:', formattedOriginalServices.map(s => ({
          id: s.id,
          name: s.serviceName,
          additionalServiceId: s.additionalServiceId,
          orderDetailId: s.orderDetailId,
          type: s.type
        })));

        // 处理追加服务订单（需要确认和支付流程）
        const formattedAdditionalOrders = additionalServiceOrders.map(item => ({
          ...item,
          type: 'additional', // 标记为追加服务订单
          createdAt: item.createdAt ? formatNormalDate(item.createdAt) : null,
          confirmTime: item.confirmTime ? formatNormalDate(item.confirmTime) : null,
          statusText: this.getAdditionalServiceStatusTextByStatus(item.status),
          // 从details中提取服务信息
          serviceName: item.details?.[0]?.serviceName || '未知服务',
          servicePrice: item.totalFee || item.originalPrice,
          needDurationTracking: item.status === 'confirmed', // 只有已确认的才需要时长统计
          additionalServiceId: item.details?.[0]?.serviceId || item.details?.[0]?.additionalServiceId, // 用于时长统计API
        }));

        // 合并所有增项服务
        const allServices = [...formattedOriginalServices, ...formattedAdditionalOrders];

        // 分离待确认和其他状态的追加服务（只有追加服务订单才有待确认状态）
        const pendingServices = formattedAdditionalOrders.filter(item => item.status === 'pending_confirm');
        const confirmedServices = allServices.filter(item =>
          item.type === 'original' || (item.type === 'additional' && item.status !== 'pending_confirm')
        );

        // 只获取已确认的追加服务订单（不包括主订单增项）
        const confirmedAdditionalServiceOrders = formattedAdditionalOrders.filter(item => item.status !== 'pending_confirm');

        console.log('🔍 增项服务分类结果:', {
          主订单增项: formattedOriginalServices.length,
          待确认追加: pendingServices.length,
          已确认服务: confirmedServices.length,
          待确认详情: pendingServices.map(s => ({ id: s.id, name: s.serviceName, status: s.status })),
          已确认详情: confirmedServices.map(s => ({ id: s.id, name: s.serviceName, status: s.status, type: s.type }))
        });

        this.setData({
          pendingAdditionalServices: pendingServices, // 待确认的追加服务订单
          allAdditionalServices: confirmedServices, // 所有已确认的服务（包括主订单增项和已确认的追加服务）
          confirmedAdditionalServiceOrders: confirmedAdditionalServiceOrders, // 只包含已确认的追加服务订单（不包括主订单增项）
          originalAdditionalServices: formattedOriginalServices, // 主订单增项服务
          additionalServiceSummary: summary, // 统计信息
        });

        console.log('📦 增项服务加载完成:', {
          待确认: pendingServices.length,
          已确认: confirmedServices.length,
          总计: allServices.length
        });

        // 加载完增项服务后，如果服务时长记录已存在，则重新计算显示数据
        setTimeout(() => {
          if (this.data.additionalServiceRecords && this.data.additionalServiceRecords.length > 0) {
            console.log('🔍 增项服务加载完成，重新计算显示数据');
            const computedData = this.computeServiceDisplayData();
            this.setData({
              orderDetail: computedData.orderDetail,
              allAdditionalServices: this.data.allAdditionalServices
            });
          } else {
            console.log('🔍 增项服务加载完成，但服务时长记录尚未加载，跳过计算');
          }
        }, 100);
      } else {
        console.log('🔍 增项服务API返回数据为空或格式错误');
        this.setData({
          pendingAdditionalServices: [],
          allAdditionalServices: [],
          originalAdditionalServices: [],
          additionalServiceSummary: {},
        });
      }

      // 重置加载状态
      this.setData({
        'loadingStates.additionalServices': false
      });
    } catch (error) {
      console.error('❌ 加载追加服务失败:', error);
      this.setData({
        pendingAdditionalServices: [],
        allAdditionalServices: [],
        confirmedAdditionalServiceOrders: [],
        originalAdditionalServices: [],
        additionalServiceSummary: {},
        'loadingStates.additionalServices': false
      });
    }
  },

  // 获取追加服务状态文字（根据status字段）
  getAdditionalServiceStatusTextByStatus(status) {
    const statusMap = {
      pending_confirm: '待确认',
      confirmed: '已确认',
      rejected: '已拒绝',
      pending_payment: '待付款',
      paid: '已付款',
      completed: '已完成',
      cancelled: '已取消',
      refunding: '退款中',
      refunded: '已退款',
    };
    return statusMap[status] || status;
  },

  // 加载评价数据
  async loadReviewData(orderId) {
    this.setData({ reviewLoading: true });

    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      if (reviewData) {
        // 处理评价图片数据
        let images = [];
        if (reviewData.images) {
          if (typeof reviewData.images === 'string') {
            try {
              images = JSON.parse(reviewData.images);
            } catch (e) {
              images = reviewData.images.split(',').filter(img => img.trim());
            }
          } else if (Array.isArray(reviewData.images)) {
            images = reviewData.images;
          }
        }

        // 格式化评价数据
        const formattedReviewData = {
          ...reviewData,
          createdAt: reviewData.createdAt ? formatNormalDate(reviewData.createdAt) : '',
          ratingStars: this.generateStars(reviewData.rating || 0),
          images: images,
        };

        this.setData({
          reviewData: formattedReviewData,
          hasReview: true,
        });
      } else {
        this.setData({
          reviewData: null,
          hasReview: false,
        });
      }
    } catch (error) {
      console.error('加载评价数据失败:', error);
      this.setData({
        reviewData: null,
        hasReview: false,
      });
    } finally {
      this.setData({ reviewLoading: false });
    }
  },

  // 生成星级评分显示
  generateStars(rating) {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    // 添加实心星星
    for (let i = 0; i < fullStars; i++) {
      stars.push('★');
    }

    // 添加半星（如果有）
    if (hasHalfStar) {
      stars.push('☆');
    }

    // 添加空星星
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push('☆');
    }

    return stars.join('');
  },

  // 预览评价图片
  previewImage(e) {
    const { current, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: current,
      urls: urls,
    });
  },

  // 通过API加载订单详情
  async loadOrderDetail() {
    wx.showLoading({ title: '加载中...' });
    try {
      // 首先尝试从本地存储获取（订单列表页面已经存储了数据）
      const info = wx.getStorageSync('orderInfo');

      if (info) {
        const formattedOrder = {
          ...info,
          orderId: info.id,
          orderNumber: info.sn,
          status: info.status,
          statusText: info.status,
          userRemark: info.orderDetails?.[0].userRemark,
          productName: info.orderDetails?.[0].service.serviceName,
          productImage: info.orderDetails?.[0].service.logo,
          petName: info.orderDetails?.[0].petName,
          userAdress: info.addressDetail + '(' + info.addressRemark + ')',
          quantity: 1,
          expectTime: formatNormalDate(info.serviceTime),
          serviceTime: info.serviceTime,
          extraServive: (info.orderDetails?.[0].additionalServices || []).map(v => v.name),
        };

        this.setOrderDetail(formattedOrder);
        return;
      }

      wx.showToast({
        title: '获取订单信息失败',
        icon: 'none',
      });
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },
  // 获取订单状态文字
  getOrderStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      paid: '待接单',
      completed: '进行中',
      review: '待评价',
    };
    return statusMap[status] || '未知状态';
  },
  // 切换更多操作弹窗
  toggleOrderActions() {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },
  // 提交订单
  submitOrder() {
    // 验证表单信息
    if (!this.validateForm()) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '提交订单中',
    });

    // 这里应该调用后端API提交订单
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 跳转到订单列表或详情页
          wx.navigateTo({
            url: '/pages/orderList/orderList',
          });
        },
      });
    }, 1500);
  },

  // 取消订单
  cancelOrder() {
    wx.navigateBack({
      delta: 1,
    });
  },

  // 表单验证
  validateForm() {
    const { customerName, customerPhone, customerAddress, quantity } = this.data;
    return customerName && customerPhone && customerAddress && quantity > 0;
  },

  // 联系客户
  contactCustomer() {
    const { orderDetail } = this.data;

    // 检查订单状态
    if (orderDetail.status !== '待服务') {
      wx.showToast({
        title: '只有待服务状态的订单才能联系客户',
        icon: 'none',
      });
      return;
    }

    const phoneNumber = orderDetail.customer?.phone || orderDetail.customer?.mobile;

    if (!phoneNumber) {
      wx.showToast({
        title: '客户手机号不存在',
        icon: 'none',
      });
      return;
    }

    wx.showModal({
      title: '联系客户',
      content: `确定要拨打客户电话 ${phoneNumber} 吗？`,
      success: res => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phoneNumber,
            success: () => {
              console.log('拨打电话成功');
            },
            fail: err => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打电话失败',
                icon: 'none',
              });
            },
          });
        }
      },
    });
  },

  // 修改上门时间
  reschedule() {
    const { orderDetail } = this.data;

    // 关闭更多操作菜单
    this.setData({
      showMoreActions: false,
    });

    // 检查订单状态
    if (orderDetail.status !== '待服务') {
      wx.showToast({
        title: '只有待服务状态的订单才能修改上门时间',
        icon: 'none',
      });
      return;
    }

    this.setData({
      showTimePicker: true,
      selectedTime: '',
    });
  },

  // 时间选择器确认
  onTimeSelected(e) {
    const selectedTime = e.detail;
    this.setData({
      selectedTime,
    });

    // 确认修改时间
    if (selectedTime) {
      this.confirmUpdateTime();
    }
  },

  // 确认修改时间
  async confirmUpdateTime() {
    if (!this.data.selectedTime) {
      wx.showToast({
        title: '请选择有效时间',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '修改中...',
    });

    try {
      const res = await orderApi.updateServiceTime(
        this.data.orderDetail.orderId || this.data.orderDetail.id,
        this.data.userInfo.id,
        new Date(this.data.selectedTime).toISOString()
      );

      if (res) {
        wx.showToast({
          title: '修改成功',
          icon: 'success',
        });

        // 更新本地订单数据
        const updatedOrderDetail = {
          ...this.data.orderDetail,
          serviceTime: formatNormalDate(this.data.selectedTime),
        };
        this.setData({
          orderDetail: updatedOrderDetail,
        });

        // 更新本地存储
        wx.setStorageSync('orderInfo', updatedOrderDetail);
      } else {
        wx.showToast({
          title: '修改失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '修改失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
      // 关闭时间选择器
      this.setData({
        showTimePicker: false,
        selectedTime: '',
      });
    }
  },

  // 取消时间选择
  onTimeCancel() {
    this.setData({
      showTimePicker: false,
      selectedTime: '',
    });
  },

  // 修改服务地址
  editServiceAddress() {
    const orderDetail = this.data.orderDetail;

    // 关闭更多操作菜单
    this.setData({
      showMoreActions: false,
    });

    // 检查订单状态权限
    if (!this.canEditAddress(orderDetail)) {
      wx.showToast({
        title: '当前订单状态不允许修改地址',
        icon: 'none',
      });
      return;
    }

    this.setData({
      showAddressEditor: true,
    });
  },

  // 检查是否可以修改地址
  canEditAddress(orderDetail) {
    const orderStatus = orderDetail.status;

    // 员工端权限：只能在出发前修改（待付款、待接单、待服务）
    const allowedStatuses = ['待付款', '待接单', '待服务'];
    return allowedStatuses.includes(orderStatus);
  },

  // 地址编辑确认
  async onAddressEditConfirm(e) {
    const addressData = e.detail;
    const orderDetail = this.data.orderDetail;

    wx.showLoading({
      title: '修改中...',
    });

    try {
      // 构建请求数据
      const requestData = {
        ...addressData,
        employeeId: this.data.userInfo.id,
        userType: 'employee', // 员工端
      };

      const result = await orderApi.updateServiceAddress(orderDetail.orderId || orderDetail.id, requestData);

      if (result) {
        wx.showToast({
          title: '地址修改成功',
          icon: 'success',
        });

        // 关闭编辑器
        this.setData({
          showAddressEditor: false,
        });

        // 更新本地订单数据
        const updatedOrderDetail = {
          ...orderDetail,
          address: addressData.address,
          addressDetail: addressData.addressDetail,
          addressRemark: addressData.addressRemark,
          latitude: addressData.latitude,
          longitude: addressData.longitude,
        };

        this.setData({
          orderDetail: updatedOrderDetail,
        });

        // 更新本地存储
        wx.setStorageSync('orderInfo', updatedOrderDetail);
      } else {
        wx.showToast({
          title: '修改失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('修改地址失败:', error);

      // 根据错误类型显示不同的提示信息
      let errorMessage = '修改失败';
      if (error && error.message) {
        if (error.message.includes('不允许修改')) {
          errorMessage = '当前订单状态不允许修改地址';
        } else if (error.message.includes('权限')) {
          errorMessage = '没有修改权限';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络错误，请重试';
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 地址编辑取消
  onAddressEditCancel() {
    this.setData({
      showAddressEditor: false,
    });
  },

  // 切换更多操作菜单
  toggleMoreActions() {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },

  // 关闭更多操作菜单
  closeMoreActions() {
    this.setData({
      showMoreActions: false,
    });
  },

  // 查看评价
  async viewReview() {
    const orderId = this.data.orderDetail.orderId || this.data.orderDetail.id;

    wx.showLoading({
      title: '加载中...',
    });

    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      if (reviewData) {
        // 将评价信息存储到本地存储，供评价详情页使用
        wx.setStorageSync('reviewInfo', reviewData);
        wx.navigateTo({
          url: `/pages/orders/reviewDetail/index?orderId=${orderId}`,
        });
      } else {
        wx.showToast({
          title: '暂无评价信息',
          icon: 'none',
        });
      }
    } catch (error) {
      console.error('获取评价信息失败:', error);
      wx.showToast({
        title: '获取评价失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 打开导航
  openNavigation(e) {
    const { address, remark, latitude, longitude } = e.currentTarget.dataset;

    // 使用统一的地址工具
    AddressUtils.openNavigation({
      address,
      remark,
      latitude,
      longitude
    });
  },

  // 确认追加服务
  confirmAdditionalService(e) {
    const service = e.currentTarget.dataset.service;
    const serviceName = service.details?.[0]?.serviceName || service.serviceName || '追加服务';

    wx.showModal({
      title: '确认追加服务',
      content: `确定要确认客户申请的"${serviceName}"追加服务吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.confirmAdditionalService(
              service.orderDetailId || this.data.orderDetail.orderDetails[0].id,
              service.id,
              this.data.userInfo.id
            );

            if (result) {
              wx.showToast({
                title: '确认成功',
                icon: 'success',
              });

              // 刷新追加服务列表（重新加载会自动分离待确认和其他状态）
              if (this.data.orderDetail.orderDetails && this.data.orderDetail.orderDetails.length > 0) {
                this.loadAllAdditionalServices(this.data.orderDetail.orderDetails[0].id);
              }

              // 通知订单列表页面刷新数据
              this.notifyOrderListRefresh();
            } else {
              wx.showToast({
                title: '确认失败',
                icon: 'error',
              });
            }
          } catch (error) {
            console.error('确认追加服务失败:', error);
            wx.showToast({
              title: '确认失败',
              icon: 'error',
            });
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 拒绝追加服务
  rejectAdditionalService(e) {
    const service = e.currentTarget.dataset.service;
    this.setData({
      currentAdditionalService: service,
      showRejectModal: true,
      rejectReason: '',
    });
  },

  // 输入拒绝原因
  onRejectReasonInput(e) {
    this.setData({
      rejectReason: e.detail.value,
    });
  },

  // 确认拒绝
  async confirmReject() {
    if (!this.data.rejectReason.trim()) {
      wx.showToast({
        title: '请输入拒绝原因',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    try {
      const service = this.data.currentAdditionalService;
      const result = await orderApi.rejectAdditionalService(
        service.orderDetailId || this.data.orderDetail.orderDetails[0].id,
        service.id,
        this.data.userInfo.id,
        this.data.rejectReason
      );

      if (result) {
        wx.showToast({
          title: '已拒绝',
          icon: 'success',
        });

        // 关闭拒绝模态框
        this.setData({
          showRejectModal: false,
          currentAdditionalService: null,
          rejectReason: '',
        });

        // 刷新追加服务列表（重新加载会自动分离待确认和其他状态）
        if (this.data.orderDetail.orderDetails && this.data.orderDetail.orderDetails.length > 0) {
          this.loadAllAdditionalServices(this.data.orderDetail.orderDetails[0].id);
        }

        // 通知订单列表页面刷新数据
        this.notifyOrderListRefresh();
      } else {
        wx.showToast({
          title: '拒绝失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('拒绝追加服务失败:', error);
      wx.showToast({
        title: '拒绝失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 取消拒绝
  cancelReject() {
    this.setData({
      showRejectModal: false,
      currentAdditionalService: null,
      rejectReason: '',
    });
  },



  // 通知订单列表页面刷新数据
  notifyOrderListRefresh() {
    // 获取页面栈中的订单列表页面
    const pages = getCurrentPages();
    const orderListPage = pages.find(page => page.route === 'pages/orders/index');

    if (orderListPage && orderListPage.resetAndReload) {
      // 调用订单列表页面的刷新方法
      orderListPage.resetAndReload();
    } else {
      // 如果找不到订单列表页面，使用事件总线通知
      getApp().globalData.needRefreshOrderList = true;
    }
  },

  // 特殊情况说明相关方法

  // 加载特殊情况说明
  async loadSpecialNote(orderId) {
    try {
      const noteData = await specialNoteApi.get(orderId);
      this.setData({
        specialNoteData: noteData,
      });
    } catch (error) {
      console.log('获取特殊情况说明失败或不存在:', error);
      this.setData({
        specialNoteData: null,
      });
    }
  },

  // 显示特殊情况说明弹窗
  showSpecialNote() {
    const { orderDetail } = this.data;

    // 根据订单状态判断是否为只读模式
    const readonly = orderDetail.status === '已完成' || orderDetail.status === '已评价';

    this.setData({
      showSpecialNote: true,
      specialNoteReadonly: readonly,
    });
  },

  // 特殊情况说明确认提交
  async onSpecialNoteConfirm(e) {
    const { content, photoList } = e.detail;
    const { orderDetail, specialNoteData, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;
    const employeeId = userInfo.id;

    wx.showLoading({
      title: '保存中...',
    });

    try {
      let result;
      if (specialNoteData) {
        // 更新已有的特殊情况说明
        result = await specialNoteApi.update(orderId, employeeId, content, photoList);
      } else {
        // 创建新的特殊情况说明
        result = await specialNoteApi.create(orderId, employeeId, content, photoList);
      }

      if (result) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
        });
        // 重新加载特殊情况说明数据
        this.loadSpecialNote(orderId);
        this.onSpecialNoteCancel();
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('保存特殊情况说明失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 特殊情况说明取消
  onSpecialNoteCancel() {
    this.setData({
      showSpecialNote: false,
      specialNoteReadonly: false,
    });
  },

  // 删除特殊情况说明
  async onSpecialNoteDelete() {
    const { orderDetail, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;
    const employeeId = userInfo.id;

    wx.showLoading({
      title: '删除中...',
    });

    try {
      const result = await specialNoteApi.delete(orderId, employeeId);
      if (result) {
        wx.showToast({
          title: '删除成功',
          icon: 'success',
        });
        // 重新加载特殊情况说明数据
        this.loadSpecialNote(orderId);
        this.onSpecialNoteCancel();
      } else {
        wx.showToast({
          title: '删除失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('删除特殊情况说明失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 预览特殊情况说明图片
  previewSpecialNotePhoto(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;

    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  // ==================== 服务时长统计相关方法 ====================

  // 格式化时长显示（友好格式）
  formatDuration(minutes) {
    if (minutes < 1) {
      return '不到1分钟';
    }

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours === 0) {
      return `${mins}分钟`;
    } else if (mins === 0) {
      return `${hours}小时`;
    } else {
      return `${hours}小时${mins}分钟`;
    }
  },

  // 启动实时计时器
  startRealtimeTimer() {
    // 清除已存在的计时器
    this.stopRealtimeTimer();

    // 每30秒更新一次实时用时
    this.realtimeTimer = setInterval(() => {
      this.updateRealtimeDuration();
    }, 30000); // 30秒更新一次
  },

  // 停止实时计时器
  stopRealtimeTimer() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer);
      this.realtimeTimer = null;
    }
  },

  // 更新实时用时显示
  updateRealtimeDuration() {
    const { orderDetail, mainServiceRecords, additionalServiceRecords } = this.data;

    if (!orderDetail || orderDetail.status !== '服务中') {
      return;
    }

    let hasRunningService = false;

    // 更新主服务的实时用时
    if (orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      orderDetail.orderDetails.forEach(item => {
        const record = mainServiceRecords.find(r => r.orderDetailId == item.id);
        if (record && record.isRunning) {
          hasRunningService = true;
          const currentTime = new Date();
          const startTime = new Date(record.startTime);
          const diffMinutes = Math.floor((currentTime - startTime) / (1000 * 60));

          // 更新显示的实时用时
          item.currentDuration = this.formatDuration(diffMinutes);
        }
      });
    }

    // 更新增项服务的实时用时
    const { allAdditionalServices = [] } = this.data;
    if (allAdditionalServices && allAdditionalServices.length > 0) {
      allAdditionalServices.forEach(item => {
        // 根据服务类型确定查找记录的方式
        let record = null;
        if (item.type === 'original') {
          // 主订单增项服务：使用服务ID查找
          record = additionalServiceRecords.find(r =>
            r.recordType === 'additional_service' &&
            r.additionalServiceId == item.id
          );
        } else if (item.type === 'additional') {
          // 追加服务订单：使用订单ID查找
          record = additionalServiceRecords.find(r =>
            r.additionalServiceOrderId == item.id
          );
        }

        if (record && record.isRunning) {
          hasRunningService = true;
          const currentTime = new Date();
          const startTime = new Date(record.startTime);
          const diffMinutes = Math.floor((currentTime - startTime) / (1000 * 60));

          // 更新显示的实时用时
          item.currentDuration = this.formatDuration(diffMinutes);
        }
      });
    }

    // 如果有正在进行的服务，更新页面数据
    if (hasRunningService) {
      this.setData({
        orderDetail: orderDetail,
        allAdditionalServices: allAdditionalServices
      });
    } else {
      // 没有正在进行的服务，停止计时器
      this.stopRealtimeTimer();
    }
  },





  // 加载订单服务状态（新接口）
  async loadOrderServiceStatus(orderId) {
    try {
      const result = await serviceDurationApi.getOrderServiceStatus(orderId);

      if (result) {
        // 处理主服务数据
        const mainServices = result.mainServices || [];
        const formattedMainServices = mainServices.map(service => ({
          ...service,
          serviceRecord: service.serviceRecord ? {
            ...service.serviceRecord,
            startTime: service.serviceRecord.startTime ? formatNormalDate(service.serviceRecord.startTime) : null,
            endTime: service.serviceRecord.endTime ? formatNormalDate(service.serviceRecord.endTime) : null,
            durationText: this.formatDuration(service.serviceRecord.duration),
            isRunning: !service.serviceRecord.endTime
          } : null
        }));

        // 处理增项服务数据
        const additionalServices = result.additionalServices || [];
        const formattedAdditionalServices = additionalServices.map(service => ({
          ...service,
          serviceRecord: service.serviceRecord ? {
            ...service.serviceRecord,
            startTime: service.serviceRecord.startTime ? formatNormalDate(service.serviceRecord.startTime) : null,
            endTime: service.serviceRecord.endTime ? formatNormalDate(service.serviceRecord.endTime) : null,
            durationText: this.formatDuration(service.serviceRecord.duration),
            isRunning: !service.serviceRecord.endTime
          } : null
        }));

        // 为了兼容原有的显示逻辑，也需要设置mainServiceRecords和additionalServiceRecords
        const mainServiceRecords = formattedMainServices
          .filter(service => service.serviceRecord)
          .map(service => ({
            ...service.serviceRecord,
            orderDetailId: service.orderDetailId, // 保持原始类型
            serviceId: service.serviceId,
            serviceName: service.serviceName,
            recordType: 'main_service'
          }));

        const additionalServiceRecords = formattedAdditionalServices
          .filter(service => service.serviceRecord)
          .map(service => ({
            ...service.serviceRecord,
            additionalServiceOrderId: service.additionalServiceOrderId, // 保持原始类型
            additionalServiceId: service.serviceId,
            serviceName: service.serviceName,
            recordType: 'additional_service'
          }));

        this.setData({
          orderServiceStatus: result,
          mainServicesStatus: formattedMainServices,
          additionalServicesStatus: formattedAdditionalServices,
          serviceStatistics: result.serviceStatistics || {},
          serviceSummary: result.summary || {},
          // 兼容原有逻辑的数据
          mainServiceRecords: mainServiceRecords,
          additionalServiceRecords: additionalServiceRecords
        });



        return result;
      }
    } catch (error) {
      console.error('加载订单服务状态失败:', error);
      return null;
    }
  },

  // 加载服务时长记录
  async loadServiceDurationRecords(orderId, retryCount = 0) {
    // 防止重复请求
    if (this.data.loadingStates.serviceDurationRecords) {
      console.log('📊 服务时长记录正在加载中，跳过重复请求');
      return;
    }

    try {
      // 设置加载状态
      this.setData({
        'loadingStates.serviceDurationRecords': true
      });

      const result = await serviceDurationApi.getRecords(orderId);

      // 新接口返回格式：{ orderId, records, statistics }
      const records = result?.records || [];
      const statistics = result?.statistics || {};

      console.log('📊 ⚠️ API原始返回数据检查:');
      console.log('📊 原始records数据:', records);
      console.log('📊 原始records中的增项服务记录:', records.filter(r =>
        r.recordType === 'additional_service'
      ));
      console.log('📊 原始records中additionalServiceId=2的记录:', records.filter(r =>
        r.additionalServiceId == 2
      ));

      if (records && Array.isArray(records)) {
        // 格式化时长记录数据
        const formattedRecords = records.map(record => ({
          ...record,
          startTime: record.startTime ? formatNormalDate(record.startTime) : null,
          endTime: record.endTime ? formatNormalDate(record.endTime) : null,
          durationText: this.formatDuration(record.duration),
          isRunning: !record.endTime, // 没有结束时间表示正在进行中
          // 确保增项服务相关字段正确处理
          additionalServiceName: record.additionalServiceName || record.serviceName,
          // 处理关联对象
          employee: record.employee || {},
          service: record.service || {},
          orderDetail: record.orderDetail || {},
          additionalService: record.additionalService || {},
          additionalServiceOrder: record.additionalServiceOrder || {},
        }));

        // 分离主服务和增项服务记录
        const mainServiceRecords = formattedRecords.filter(record => record.recordType === 'main_service');
        const additionalServiceRecords = formattedRecords.filter(record => record.recordType === 'additional_service');

        // 精简日志：只检查关键的全身去油记录
        const quanshenquyouRecord = additionalServiceRecords.find(r =>
          r.recordType === 'additional_service' &&
          r.additionalServiceId == 2
        );
        console.log('📊 ⚠️ 全身去油记录查找结果:', quanshenquyouRecord ? {
          找到: true,
          ID: quanshenquyouRecord.id,
          运行中: quanshenquyouRecord.isRunning
        } : '❌ 未找到');



        // 为统计数据添加格式化文本
        const formattedStatistics = {
          ...statistics,
          totalDurationText: statistics.totalDuration ? this.formatDuration(statistics.totalDuration) : '0分钟',
          mainServiceDurationText: statistics.mainServiceDuration ? this.formatDuration(statistics.mainServiceDuration) : '0分钟',
          additionalServiceDurationText: statistics.additionalServiceDuration ? this.formatDuration(statistics.additionalServiceDuration) : '0分钟',
        };

        // 精简：只检查关键数据
        console.log('📊 设置状态前检查: 增项服务记录数=', additionalServiceRecords.length);

        // 先设置数据到页面状态
        this.setData({
          serviceDurationRecords: formattedRecords,
          mainServiceRecords: mainServiceRecords,
          additionalServiceRecords: additionalServiceRecords,
          serviceDurationStatistics: formattedStatistics, // 包含格式化文本的统计信息
        });

        // 然后计算WXML需要的数据（传入最新的记录数据）
        const computedData = this.computeServiceDisplayData(mainServiceRecords, additionalServiceRecords);

        // 更新包含计算属性的数据
        this.setData({
          orderDetail: computedData.orderDetail, // 更新包含计算属性的订单详情
          allAdditionalServices: this.data.allAdditionalServices // 确保增项服务数据也被更新
        });

        // 精简：验证数据是否正确设置
        const pageRecord = this.data.additionalServiceRecords?.find(r => r.additionalServiceId == 2);
        console.log('📊 页面状态验证: 全身去油记录=', pageRecord ? '✅存在' : '❌不存在');

        // 重置加载状态
        this.setData({
          'loadingStates.serviceDurationRecords': false
        });

        // 如果增项服务已加载，触发一次重新计算以确保显示正确
        if (this.data.allAdditionalServices && this.data.allAdditionalServices.length > 0) {
          console.log('📊 服务时长记录加载完成，重新计算增项服务显示状态');
          setTimeout(() => {
            const recomputedData = this.computeServiceDisplayData();
            this.setData({
              orderDetail: recomputedData.orderDetail
            });
          }, 50);
        }



        // 如果是服务中状态但没有主服务记录，且重试次数少于2次，则1秒后重试
        if (this.data.orderDetail?.status === '服务中' &&
            mainServiceRecords.length === 0 &&
            retryCount < 2) {

          setTimeout(() => {
            this.loadServiceDurationRecords(orderId, retryCount + 1);
          }, 1000);
        }
      }
    } catch (error) {
      console.error('加载服务时长记录失败:', error);

      // 重置加载状态
      this.setData({
        'loadingStates.serviceDurationRecords': false
      });

      // 如果是网络错误且重试次数少于2次，则重试
      if (retryCount < 2) {
        console.log(`网络错误，1秒后重试 (${retryCount + 1}/2)`);
        setTimeout(() => {
          this.loadServiceDurationRecords(orderId, retryCount + 1);
        }, 1000);
      }
    }
  },

  // 格式化时长显示
  formatDuration(minutes) {
    if (!minutes) return '0分钟';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}小时${mins}分钟`;
    }
    return `${mins}分钟`;
  },

  // 开始服务时长统计
  async startServiceDuration(serviceType, serviceData) {
    const { orderDetail, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;

    try {
      const params = {
        orderId: orderId,
        recordType: serviceType, // 'main_service' 或 'additional_service'
        remark: `开始${serviceData.serviceName || serviceData.name}服务`,
      };

      // 根据服务类型设置不同的参数
      if (serviceType === 'main_service') {
        params.orderDetailId = orderDetail.orderDetails[0].id;
        params.serviceId = orderDetail.orderDetails[0].service.id;
      } else if (serviceType === 'additional_service') {
        params.additionalServiceOrderId = serviceData.id;
        params.additionalServiceId = serviceData.additionalServiceId;
      }

      const result = await serviceDurationApi.start(params);
      if (result) {
        wx.showToast({
          title: '开始计时',
          icon: 'success',
        });
        // 重新加载服务时长记录
        this.loadServiceDurationRecords(orderId);
      }
    } catch (error) {
      console.error('开始服务时长统计失败:', error);
      wx.showToast({
        title: '开始计时失败',
        icon: 'error',
      });
    }
  },

  // 结束服务时长统计
  async endServiceDuration(recordId, serviceName) {
    try {
      const params = {
        recordId: recordId,
        remark: `完成${serviceName}服务`,
      };

      const result = await serviceDurationApi.end(params);
      if (result) {
        wx.showToast({
          title: '结束计时',
          icon: 'success',
        });
        // 重新加载服务时长记录
        const { orderDetail } = this.data;
        const orderId = orderDetail.orderId || orderDetail.id;
        await this.loadServiceDurationRecords(orderId);

        // 检查是否所有服务都已完成，如果是则提示完成整体服务
        this.checkAllServicesCompleted();
      }
    } catch (error) {
      console.error('结束服务时长统计失败:', error);
      wx.showToast({
        title: '结束计时失败',
        icon: 'error',
      });
    }
  },

  // 启动服务计时器（用于实时显示当前服务时长）
  startServiceTimer() {
    // 清除之前的计时器
    if (this.data.serviceDurationTimer) {
      clearInterval(this.data.serviceDurationTimer);
    }

    // 每分钟更新一次当前服务时长显示
    const timer = setInterval(() => {
      this.updateCurrentServiceDuration();
    }, 60000); // 60秒更新一次

    this.setData({
      serviceDurationTimer: timer,
    });
  },

  // 更新当前服务时长显示
  updateCurrentServiceDuration() {
    const { serviceDurationRecords } = this.data;
    if (serviceDurationRecords.length === 0) return;

    const now = new Date();
    const updatedRecords = serviceDurationRecords.map(record => {
      if (record.startTime && !record.endTime) {
        const startTime = new Date(record.startTime);
        const diffMinutes = Math.floor((now - startTime) / (1000 * 60));
        return {
          ...record,
          currentDuration: diffMinutes,
          currentDurationText: this.formatDuration(diffMinutes),
        };
      }
      return record;
    });

    // 重新分离主服务和增项服务记录
    const mainServiceRecords = updatedRecords.filter(record => record.recordType === 'main_service');
    const additionalServiceRecords = updatedRecords.filter(record => record.recordType === 'additional_service');

    this.setData({
      serviceDurationRecords: updatedRecords,
      mainServiceRecords: mainServiceRecords,
      additionalServiceRecords: additionalServiceRecords,
    });
  },

  // 切换服务时长统计显示
  toggleServiceDuration() {
    this.setData({
      showServiceDuration: !this.data.showServiceDuration,
    });
  },

  // 开始主服务时长统计
  async startMainService(e) {
    const { orderDetailId, serviceId, serviceName } = e.currentTarget.dataset;
    const { orderDetail, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;

    try {
      const params = {
        orderId: orderId,
        orderDetailId: orderDetailId,
        recordType: 'main_service',
        serviceId: serviceId,
        remark: `开始${serviceName}服务`,
      };

      const result = await serviceDurationApi.start(params);
      if (result) {
        wx.showToast({
          title: '开始计时',
          icon: 'success',
        });
        // 重新加载服务时长记录
        this.loadServiceDurationRecords(orderId);
        // 启动实时计时器
        this.startRealtimeTimer();
      }
    } catch (error) {
      console.error('开始主服务时长统计失败:', error);
      wx.showToast({
        title: '开始计时失败',
        icon: 'error',
      });
    }
  },

  // 开始增项服务时长统计
  async startAdditionalService(e) {
    const { serviceType, serviceId, additionalServiceId, orderDetailId, serviceName } = e.currentTarget.dataset;
    const { orderDetail, allAdditionalServices, additionalServiceRecords } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;

    console.log('🚀 ========== 点击开始增项服务按钮 ==========');
    console.log('🚀 点击参数:', {
      serviceName,
      serviceType,
      serviceId,
      additionalServiceId,
      orderDetailId,
      orderId
    });

    // 先检查当前数据状态
    const currentService = allAdditionalServices.find(s =>
      (s.type === 'original' && s.id == serviceId) ||
      (s.type === 'additional' && s.id == serviceId)
    );

    console.log('🚀 当前服务数据:', currentService ? {
      id: currentService.id,
      name: currentService.serviceName,
      type: currentService.type,
      additionalServiceId: currentService.additionalServiceId,
      orderDetailId: currentService.orderDetailId,
      showStartBtn: currentService.showStartBtn,
      showEndBtn: currentService.showEndBtn,
      serviceStatus: currentService.serviceStatus
    } : '未找到服务数据');

    // 检查是否已有对应的时长记录
    let existingRecord = null;
    if (serviceType === 'original') {
      existingRecord = additionalServiceRecords.find(r =>
        r.recordType === 'additional_service' &&
        r.additionalServiceId == additionalServiceId
      );
    } else if (serviceType === 'additional') {
      existingRecord = additionalServiceRecords.find(r =>
        r.additionalServiceOrderId == serviceId
      );
    }

    console.log('🚀 检查现有记录:', existingRecord ? '✅已存在' : '❌不存在');

    if (existingRecord) {
      console.log('⚠️ 发现已存在记录，但按钮仍显示开始状态，可能存在数据同步问题');
      // 先刷新数据再重新检查
      await this.loadServiceDurationRecords(orderId);
      wx.showToast({
        title: '数据已刷新，请重试',
        icon: 'none'
      });
      return;
    }

    try {
      let params;

      // 根据服务类型设置不同的参数
      if (serviceType === 'original') {
        // 主订单中的增项服务
        params = {
          orderId: orderId,
          recordType: 'additional_service',
          orderDetailId: orderDetailId,
          additionalServiceId: additionalServiceId,
          remark: `开始${serviceName}增项服务`,
        };
        console.log('🚀 开始主订单增项服务:', params);
      } else if (serviceType === 'additional') {
        // 追加服务中的增项服务
        params = {
          orderId: orderId,
          recordType: 'additional_service',
          additionalServiceOrderId: serviceId,
          additionalServiceId: additionalServiceId,
          remark: `开始${serviceName}增项服务`,
        };
        console.log('🚀 开始追加服务增项:', params);
      } else {
        throw new Error(`未知的服务类型: ${serviceType}`);
      }

      console.log('🚀 发送API请求:', params);
      const result = await serviceDurationApi.start(params);
      console.log('🚀 API响应结果:', result);

      if (result) {
        wx.showToast({
          title: '开始计时',
          icon: 'success',
        });
        // 重新加载服务时长记录
        this.loadServiceDurationRecords(orderId);
        // 启动实时计时器
        this.startRealtimeTimer();
      } else {
        console.log('⚠️ API返回结果为空或失败');
        wx.showToast({
          title: '开始计时失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('🚨 开始增项服务时长统计失败:', error);
      console.error('🚨 错误详情:', {
        message: error.message,
        stack: error.stack,
        params: params
      });

      // 检查是否是"已经开始"的错误
      if (error.message && error.message.includes('已经开始')) {
        console.log('🔄 检测到"已经开始"错误，刷新数据');
        await this.loadServiceDurationRecords(orderId);
        wx.showToast({
          title: '该服务已在进行中',
          icon: 'none',
        });
      } else {
        wx.showToast({
          title: error.message || '开始计时失败',
          icon: 'error',
        });
      }
    }
  },

  // 预计算WXML需要的显示数据
  computeServiceDisplayData(mainServiceRecords = null, additionalServiceRecords = null) {
    const { orderDetail } = this.data;

    // 如果没有传入参数，则从页面状态获取
    const finalMainServiceRecords = mainServiceRecords || this.data.mainServiceRecords || [];
    const finalAdditionalServiceRecords = additionalServiceRecords || this.data.additionalServiceRecords || [];

    console.log('🔍 computeServiceDisplayData 使用的数据源:', {
      传入参数: !!additionalServiceRecords,
      最终记录数: finalAdditionalServiceRecords.length,
      包含全身去油: finalAdditionalServiceRecords.some(r => r.additionalServiceId == 2)
    });

    // 为每个主服务添加计算好的显示属性
    if (orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      orderDetail.orderDetails.forEach(item => {
        const orderDetailId = item.id;
        const record = finalMainServiceRecords.find(r => r.orderDetailId == orderDetailId);

        // 添加显示属性到item对象
        item.showStartBtn = !record;
        item.showEndBtn = record && record.isRunning;
        item.serviceStatus = record ? (record.isRunning ? 'running' : 'completed') : 'not_started';
        item.serviceStatusText = record ? (record.isRunning ? '进行中' : '已完成') : '未开始';

        // 添加时间信息
        if (record) {
          item.startTime = record.startTime;
          item.recordId = record.id;

          if (record.isRunning) {
            // 正在进行的服务，计算实时用时
            const currentTime = new Date();
            const startTime = new Date(record.startTime);
            const diffMinutes = Math.floor((currentTime - startTime) / (1000 * 60));
            item.currentDuration = this.formatDuration(diffMinutes);
            item.duration = null;
          } else {
            // 已完成的服务，显示总用时
            item.duration = record.durationText;
            item.currentDuration = null;
          }
        } else {
          item.startTime = null;
          item.duration = null;
          item.currentDuration = null;
          item.recordId = null;
        }
      });
    }

    // 为每个增项服务添加计算好的显示属性
    const { allAdditionalServices = [] } = this.data;
    if (allAdditionalServices && allAdditionalServices.length > 0) {
      console.log('🔍 ========== 计算增项服务按钮显示逻辑 ==========');
      // 精简：只显示关键的全身去油服务信息
      const quanshenquyouService = allAdditionalServices.find(s => s.additionalServiceId == 2);
      console.log('🔍 全身去油服务数据:', quanshenquyouService ? {
        id: quanshenquyouService.id,
        additionalServiceId: quanshenquyouService.additionalServiceId,
        type: quanshenquyouService.type
      } : '❌未找到');

      const quanshenquyouRecord = finalAdditionalServiceRecords.find(r =>
        r.recordType === 'additional_service' && r.additionalServiceId == 2
      );
      console.log('🔍 全身去油记录数据:', quanshenquyouRecord ? {
        id: quanshenquyouRecord.id,
        isRunning: quanshenquyouRecord.isRunning
      } : '❌未找到');

      allAdditionalServices.forEach(item => {
        // 精简：只处理全身去油服务的详细日志
        let record = null;
        if (item.type === 'original') {
          record = finalAdditionalServiceRecords.find(r =>
            r.recordType === 'additional_service' &&
            r.additionalServiceId == item.additionalServiceId
          );

          // 只为全身去油输出详细日志
          if (item.additionalServiceId == 2) {
            console.log(`🔍 全身去油查找: additionalServiceId=${item.additionalServiceId}, 找到记录=${!!record}`);
          }
        } else if (item.type === 'additional') {
          record = finalAdditionalServiceRecords.find(r =>
            r.additionalServiceOrderId == item.id
          );
        }

        item.showStartBtn = !record;
        item.showEndBtn = record && record.isRunning;
        item.serviceStatus = record ? (record.isRunning ? 'running' : 'completed') : 'not_started';
        item.serviceStatusText = record ? (record.isRunning ? '进行中' : '已完成') : '未开始';

        // 更新操作按钮显示状态：只要需要统计时长且订单在服务中，就显示按钮
        item.showDurationActions = item.needDurationTracking && orderDetail.status === '服务中';

        // 只为全身去油输出最终结果
        if (item.additionalServiceId == 2) {
          console.log(`🔍 全身去油最终状态: showStartBtn=${item.showStartBtn}, showEndBtn=${item.showEndBtn}, hasRecord=${!!record}`);
        }

        if (record) {
          item.startTime = record.startTime;
          item.recordId = record.id;

          if (record.isRunning) {
            // 正在进行的服务，计算实时用时
            const currentTime = new Date();
            const startTime = new Date(record.startTime);
            const diffMinutes = Math.floor((currentTime - startTime) / (1000 * 60));
            item.currentDuration = this.formatDuration(diffMinutes);
            item.duration = null;
          } else {
            // 已完成的服务，显示总用时
            item.duration = record.durationText;
            item.currentDuration = null;
          }
        } else {
          item.startTime = null;
          item.duration = null;
          item.currentDuration = null;
          item.recordId = null;
        }
      });

      // 更新数据
      this.setData({
        allAdditionalServices: allAdditionalServices
      });

      // 精简汇总：只显示全身去油的最终状态
      const quanshenquyouFinal = allAdditionalServices.find(s => s.additionalServiceId == 2);
      console.log('🔍 ========== 全身去油最终状态 ==========');
      console.log('🔍 结果:', quanshenquyouFinal ? {
        showStart: quanshenquyouFinal.showStartBtn,
        showEnd: quanshenquyouFinal.showEndBtn,
        status: quanshenquyouFinal.serviceStatusText
      } : '❌服务未找到');
      console.log('🔍 =====================================');
    }

    return { orderDetail };
  },

  // 获取主服务记录
  getMainServiceRecord(orderDetailId) {
    const { mainServiceRecords } = this.data;
    if (!mainServiceRecords || mainServiceRecords.length === 0) {
      return null;
    }
    return mainServiceRecords.find(record => record.orderDetailId == orderDetailId);
  },



  // 获取已完成服务数量
  getCompletedServicesCount() {
    const { serviceDurationRecords } = this.data;
    return serviceDurationRecords.filter(record => !record.isRunning).length;
  },

  // 获取总用时
  getTotalDuration() {
    const { serviceDurationRecords } = this.data;
    const totalMinutes = serviceDurationRecords
      .filter(record => !record.isRunning && record.duration)
      .reduce((total, record) => total + record.duration, 0);
    return this.formatDuration(totalMinutes);
  },

  // 检查所有服务是否都已完成
  checkAllServicesCompleted() {
    const {
      orderDetail,
      serviceDurationRecords,
      allAdditionalServices,
      mainServicesStatus,
      additionalServicesStatus,
      serviceSummary
    } = this.data;

    let mainServicesCompleted = false;
    let additionalServicesCompleted = false;

    // 优先使用新的服务状态数据
    if (serviceSummary && serviceSummary.totalMainServices !== undefined) {
      mainServicesCompleted = serviceSummary.completedMainServices === serviceSummary.totalMainServices;
      additionalServicesCompleted = serviceSummary.completedAdditionalServices === serviceSummary.totalAdditionalServices;
    } else {
      // 回退到原有逻辑
      mainServicesCompleted = orderDetail.orderDetails?.every(detail => {
        const record = this.getMainServiceRecord(detail.id);
        return record && !record.isRunning;
      }) || false;

      // 检查需要统计时长的增项服务是否都已完成
      const needTrackingAdditionalServices = allAdditionalServices?.filter(service =>
        service.status === 'confirmed' && service.needDurationTracking !== false
      ) || [];

      additionalServicesCompleted = needTrackingAdditionalServices.length === 0 ||
        needTrackingAdditionalServices.every(service => {
          const { additionalServiceRecords } = this.data;
          const record = additionalServiceRecords.find(r => r.additionalServiceOrderId == service.id);
          return record && !record.isRunning;
        });
    }

    // 如果所有服务都已完成，提示用户完成整体服务
    if (mainServicesCompleted && additionalServicesCompleted && orderDetail.status === '服务中') {
      wx.showModal({
        title: '服务完成提醒',
        content: '所有服务项目都已完成，是否完成整体服务？',
        confirmText: '完成服务',
        cancelText: '稍后完成',
        success: (res) => {
          if (res.confirm) {
            this.completeOverallService();
          }
        }
      });
    }
  },

  // 完成整体服务
  async completeOverallService() {
    const { orderDetail, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;

    wx.showModal({
      title: '确认完成服务',
      content: '确定要完成整体服务吗？',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            // 调用完成服务接口
            const result = await orderApi.complete(orderId, userInfo.id);
            if (result) {
              wx.showToast({
                title: '服务已完成',
                icon: 'success',
              });

              // 返回订单列表并刷新
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'error',
              });
            }
          } catch (error) {
            console.error('完成服务失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'error',
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  },



  // 页面卸载时清除计时器
  onUnload() {
    // 清除旧的计时器
    if (this.data.serviceDurationTimer) {
      clearInterval(this.data.serviceDurationTimer);
    }

    // 清除实时计时器
    this.stopRealtimeTimer();
  },
});
